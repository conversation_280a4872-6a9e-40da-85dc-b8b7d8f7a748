name: droidenv_vis
channels:
  - rusty1s
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_kmp_llvm
  - absl-py=1.4.0=pyhd8ed1ab_0
  - aiohttp=3.8.4=py39h72bdee0_0
  - aiosignal=1.3.1=pyhd8ed1ab_0
  - alsa-lib=1.2.8=h166bdaf_0
  - aom=3.5.0=h27087fc_0
  - async-timeout=4.0.2=pyhd8ed1ab_0
  - attr=2.5.1=h166bdaf_1
  - attrs=22.2.0=pyh71513ae_0
  - blas=2.116=mkl
  - blas-devel=3.9.0=16_linux64_mkl
  - brotli=1.0.9=h166bdaf_8
  - brotli-bin=1.0.9=h166bdaf_8
  - brotlipy=0.7.0=py39hb9d737c_1005
  - bzip2=1.0.8=h7f98852_4
  - c-ares=1.18.1=h7f98852_0
  - ca-certificates=2022.12.7=ha878542_0
  - cachetools=5.3.0=pyhd8ed1ab_0
  - cairo=1.16.0=ha61ee94_1014
  - certifi=2022.12.7=pyhd8ed1ab_0
  - cffi=1.15.1=py39he91dace_3
  - charset-normalizer=2.1.1=pyhd8ed1ab_0
  - click=8.1.3=py39hf3d152e_1
  - colorama=0.4.6=pyhd8ed1ab_0
  - contourpy=1.0.7=py39h4b4f3f3_0
  - cryptography=39.0.2=py39h079d5ae_0
  - cudatoolkit=11.3.1=ha36c431_9
  - cycler=0.11.0=pyhd8ed1ab_0
  - dbus=1.13.6=h5008d03_3
  - expat=2.5.0=h27087fc_0
  - ffmpeg=5.1.2=gpl_h8dda1f0_106
  - fftw=3.3.10=nompi_hf0379b8_106
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=hab24e00_0
  - fontconfig=2.14.2=h14ed4e7_0
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - fonttools=4.39.0=py39h72bdee0_0
  - freeglut=3.2.2=h9c3ff4c_1
  - freetype=2.12.1=hca18f0e_1
  - frozenlist=1.3.3=py39hb9d737c_0
  - gettext=0.21.1=h27087fc_0
  - glib=2.74.1=h6239696_1
  - glib-tools=2.74.1=h6239696_1
  - gmp=6.2.1=h58526e2_0
  - gnutls=3.7.8=hf3e180e_0
  - google-auth=2.16.2=pyh1a96a4e_0
  - google-auth-oauthlib=0.4.6=pyhd8ed1ab_0
  - graphite2=1.3.13=h58526e2_1001
  - grpcio=1.52.1=py39h227be39_1
  - gst-plugins-base=1.22.0=h4243ec0_2
  - gstreamer=1.22.0=h25f0c4b_2
  - gstreamer-orc=0.4.33=h166bdaf_0
  - harfbuzz=6.0.0=h8e241bc_0
  - hdf5=1.14.0=nompi_hb72d44e_103
  - icu=70.1=h27087fc_0
  - idna=3.4=pyhd8ed1ab_0
  - importlib-metadata=6.0.0=pyha770c72_0
  - importlib-resources=5.12.0=pyhd8ed1ab_0
  - importlib_resources=5.12.0=pyhd8ed1ab_0
  - jack=1.9.22=h11f4161_0
  - jasper=2.0.33=h0ff4b12_1
  - jpeg=9e=h0b41bf4_3
  - keyutils=1.6.1=h166bdaf_0
  - kiwisolver=1.4.4=py39hf939315_1
  - krb5=1.20.1=h81ceb04_0
  - lame=3.100=h166bdaf_1003
  - lcms2=2.15=hfd0df8a_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - lerc=4.0.0=h27087fc_0
  - libabseil=20230125.0=cxx17_hcb278e6_1
  - libaec=1.0.6=hcb278e6_1
  - libblas=3.9.0=16_linux64_mkl
  - libbrotlicommon=1.0.9=h166bdaf_8
  - libbrotlidec=1.0.9=h166bdaf_8
  - libbrotlienc=1.0.9=h166bdaf_8
  - libcap=2.66=ha37c62d_0
  - libcblas=3.9.0=16_linux64_mkl
  - libclang=15.0.7=default_had23c3d_1
  - libclang13=15.0.7=default_h3e3d535_1
  - libcups=2.3.3=h36d4200_3
  - libcurl=7.88.1=hdc1c0ab_0
  - libdb=6.2.32=h9c3ff4c_0
  - libdeflate=1.17=h0b41bf4_0
  - libdrm=2.4.114=h166bdaf_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=h516909a_1
  - libevent=2.1.10=h28343ad_4
  - libffi=3.4.2=h7f98852_5
  - libflac=1.4.2=h27087fc_0
  - libgcc-ng=12.2.0=h65d4601_19
  - libgcrypt=1.10.1=h166bdaf_0
  - libgfortran-ng=12.2.0=h69a702a_19
  - libgfortran5=12.2.0=h337968e_19
  - libglib=2.74.1=h606061b_1
  - libglu=9.0.0=he1b5a44_1001
  - libgpg-error=1.46=h620e276_0
  - libgrpc=1.52.1=hcf146ea_1
  - libhwloc=2.9.0=hd6dc26d_0
  - libiconv=1.17=h166bdaf_0
  - libidn2=2.3.4=h166bdaf_0
  - liblapack=3.9.0=16_linux64_mkl
  - liblapacke=3.9.0=16_linux64_mkl
  - libllvm15=15.0.7=hadd5161_1
  - libnghttp2=1.52.0=h61bc06f_0
  - libnsl=2.0.0=h7f98852_0
  - libogg=1.3.4=h7f98852_1
  - libopencv=4.7.0=py39h2ca4621_1
  - libopus=1.3.1=h7f98852_1
  - libpciaccess=0.17=h166bdaf_0
  - libpng=1.6.39=h753d276_0
  - libpq=15.2=hb675445_0
  - libprotobuf=3.21.12=h3eb15da_0
  - libsndfile=1.2.0=hb75c966_0
  - libsqlite=3.40.0=h753d276_0
  - libssh2=1.10.0=hf14f497_3
  - libstdcxx-ng=12.2.0=h46fd767_19
  - libsystemd0=252=h2a991cd_0
  - libtasn1=4.19.0=h166bdaf_0
  - libtiff=4.5.0=h6adf6a1_2
  - libtool=2.4.7=h27087fc_0
  - libudev1=253=h0b41bf4_0
  - libunistring=0.9.10=h7f98852_0
  - libuuid=2.32.1=h7f98852_1000
  - libuv=1.44.2=h166bdaf_0
  - libva=2.17.0=h0b41bf4_0
  - libvorbis=1.3.7=h9c3ff4c_0
  - libvpx=1.11.0=h9c3ff4c_3
  - libwebp-base=1.3.0=h0b41bf4_0
  - libxcb=1.13=h7f98852_1004
  - libxkbcommon=1.5.0=h79f4944_1
  - libxml2=2.10.3=hca2bb57_3
  - libzlib=1.2.13=h166bdaf_4
  - llvm-openmp=15.0.7=h0cdce71_0
  - lz4-c=1.9.4=hcb278e6_0
  - markdown=3.4.1=pyhd8ed1ab_0
  - markupsafe=2.1.2=py39h72bdee0_0
  - matplotlib=3.7.1=py39hf3d152e_0
  - matplotlib-base=3.7.1=py39he190548_0
  - metis=5.1.0=h58526e2_1006
  - mkl=2022.1.0=h84fe81f_915
  - mkl-devel=2022.1.0=ha770c72_916
  - mkl-include=2022.1.0=h84fe81f_915
  - mpfr=4.2.0=hb012696_0
  - mpg123=1.31.2=hcb278e6_0
  - multidict=6.0.4=py39h72bdee0_0
  - munkres=1.1.4=pyh9f0ad1d_0
  - mysql-common=8.0.32=ha901b37_0
  - mysql-libs=8.0.32=hd7da12d_0
  - ncurses=6.3=h27087fc_1
  - nettle=3.8.1=hc379101_1
  - nspr=4.35=h27087fc_0
  - nss=3.89=he45b914_0
  - numpy=1.24.2=py39h7360e5f_0
  - oauthlib=3.2.2=pyhd8ed1ab_0
  - opencv=4.7.0=py39hf3d152e_1
  - openh264=2.3.1=hcb278e6_2
  - openjpeg=2.5.0=hfec8fc6_2
  - openssl=3.1.0=h0b41bf4_0
  - p11-kit=0.24.1=hc5aa10d_0
  - packaging=23.0=pyhd8ed1ab_0
  - pcre2=10.40=hc3806b6_0
  - pillow=9.4.0=py39h2320bf1_1
  - pip=23.0.1=pyhd8ed1ab_0
  - pixman=0.40.0=h36c2ea0_0
  - platformdirs=3.1.1=pyhd8ed1ab_0
  - ply=3.11=py_1
  - pooch=1.7.0=pyhd8ed1ab_0
  - protobuf=4.21.12=py39h227be39_0
  - pthread-stubs=0.4=h36c2ea0_1001
  - pulseaudio=16.1=ha8d29e2_1
  - py-opencv=4.7.0=py39hcca971b_1
  - pyasn1=0.4.8=py_0
  - pyasn1-modules=0.2.7=py_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pyjwt=2.6.0=pyhd8ed1ab_0
  - pyopenssl=23.0.0=pyhd8ed1ab_0
  - pyparsing=3.0.9=pyhd8ed1ab_0
  - pyqt=5.15.7=py39h5c7b992_3
  - pyqt5-sip=12.11.0=py39h227be39_3
  - pysocks=1.7.1=py39hf3d152e_5
  - python=3.9.16=h2782a2a_0_cpython
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.9=3_cp39
  - pytorch=1.10.2=py3.9_cuda11.3_cudnn8.2.0_0
  - pytorch-mutex=1.0=cuda
  - pytorch-scatter=2.0.9=py39_torch_1.10.0_cu113
  - pyu2f=0.1.5=pyhd8ed1ab_0
  - pyyaml=6.0=py39hb9d737c_5
  - qt-main=5.15.8=h5d23da1_6
  - re2=2023.02.02=hcb278e6_0
  - readline=8.1.2=h0f457ee_0
  - requests=2.28.2=pyhd8ed1ab_0
  - requests-oauthlib=1.3.1=pyhd8ed1ab_0
  - rsa=4.9=pyhd8ed1ab_0
  - scipy=1.10.1=py39h7360e5f_0
  - setuptools=67.6.0=pyhd8ed1ab_0
  - sip=6.7.7=py39h227be39_0
  - six=1.16.0=pyh6c4a22f_0
  - suitesparse=5.10.1=h9e50725_1
  - svt-av1=1.4.1=hcb278e6_0
  - tbb=2021.8.0=hf52228f_0
  - tensorboard=2.12.0=pyhd8ed1ab_0
  - tensorboard-data-server=0.7.0=py39h079d5ae_0
  - tensorboard-plugin-wit=1.8.1=pyhd8ed1ab_0
  - tk=8.6.12=h27826a3_0
  - toml=0.10.2=pyhd8ed1ab_0
  - torchaudio=0.10.2=py39_cu113
  - torchvision=0.11.3=py39_cu113
  - tornado=6.2=py39hb9d737c_1
  - tqdm=4.65.0=pyhd8ed1ab_1
  - typing-extensions=4.5.0=hd8ed1ab_0
  - typing_extensions=4.5.0=pyha770c72_0
  - tzdata=2022g=h191b570_0
  - unicodedata2=15.0.0=py39hb9d737c_0
  - urllib3=1.26.15=pyhd8ed1ab_0
  - wheel=0.40.0=pyhd8ed1ab_0
  - x264=1!164.3095=h166bdaf_2
  - x265=3.5=h924138e_3
  - xcb-util=0.4.0=h166bdaf_0
  - xcb-util-image=0.4.0=h166bdaf_0
  - xcb-util-keysyms=0.4.0=h166bdaf_0
  - xcb-util-renderutil=0.3.9=h166bdaf_0
  - xcb-util-wm=0.4.1=h166bdaf_0
  - xkeyboard-config=2.38=h0b41bf4_0
  - xorg-fixesproto=5.0=h7f98852_1002
  - xorg-inputproto=2.3.2=h7f98852_1002
  - xorg-kbproto=1.0.7=h7f98852_1002
  - xorg-libice=1.0.10=h7f98852_0
  - xorg-libsm=1.2.3=hd9c2040_1000
  - xorg-libx11=1.8.4=h0b41bf4_0
  - xorg-libxau=1.0.9=h7f98852_0
  - xorg-libxdmcp=1.1.3=h7f98852_0
  - xorg-libxext=1.3.4=h0b41bf4_2
  - xorg-libxfixes=5.0.3=h7f98852_1004
  - xorg-libxi=1.7.10=h7f98852_0
  - xorg-libxrender=0.9.10=h7f98852_1003
  - xorg-renderproto=0.11.1=h7f98852_1002
  - xorg-xextproto=7.3.0=h0b41bf4_1003
  - xorg-xproto=7.0.31=h7f98852_1007
  - xz=5.2.6=h166bdaf_0
  - yaml=0.2.5=h7f98852_2
  - yarl=1.8.2=py39hb9d737c_0
  - zipp=3.15.0=pyhd8ed1ab_0
  - zlib=1.2.13=h166bdaf_4
  - zstd=1.5.2=h3eb15da_6
  - pip:
    - addict==2.4.0
    - argcomplete==2.1.1
    - asttokens==2.2.1
    - backcall==0.2.0
    - blinker==1.6.2
    - comm==0.1.3
    - configargparse==1.5.3
    - dash==2.9.3
    - dash-core-components==2.0.0
    - dash-html-components==2.0.0
    - dash-table==5.0.0
    - debugpy==1.6.7
    - decorator==5.1.1
    - evo==1.22.0
    - executing==1.2.0
    - fastjsonschema==2.17.1
    - flask==2.3.2
    - flow-vis==0.1
    - ipykernel==6.23.1
    - ipython==8.13.2
    - ipywidgets==8.0.6
    - itsdangerous==2.1.2
    - jedi==0.18.2
    - jinja2==3.1.2
    - joblib==1.2.0
    - jsonschema==4.17.3
    - jupyter-client==8.2.0
    - jupyter-core==5.3.0
    - jupyterlab-widgets==3.0.7
    - lz4==4.3.2
    - matplotlib-inline==0.1.6
    - natsort==8.3.1
    - nbformat==5.7.0
    - nest-asyncio==1.5.6
    - numexpr==2.8.4
    - open3d==0.17.0
    - pandas==1.5.3
    - parso==0.8.3
    - pexpect==4.8.0
    - pickleshare==0.7.5
    - plotly==5.14.1
    - prompt-toolkit==3.0.38
    - psutil==5.9.5
    - ptyprocess==0.7.0
    - pure-eval==0.2.2
    - pygments==2.14.0
    - pyquaternion==0.9.9
    - pyrsistent==0.19.3
    - pytz==2022.7.1
    - pyzmq==25.0.2
    - rosbags==0.9.15
    - ruamel-yaml==0.17.21
    - ruamel-yaml-clib==0.2.7
    - scikit-learn==1.2.2
    - seaborn==0.12.2
    - stack-data==0.6.2
    - tenacity==8.2.2
    - threadpoolctl==3.1.0
    - traitlets==5.9.0
    - wcwidth==0.2.6
    - werkzeug==2.3.4
    - widgetsnbextension==4.0.7
    - zstandard==0.20.0
