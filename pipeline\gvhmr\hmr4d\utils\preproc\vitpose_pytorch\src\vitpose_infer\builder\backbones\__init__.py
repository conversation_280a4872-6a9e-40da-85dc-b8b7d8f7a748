# Copyright (c) OpenMMLab. All rights reserved.
# from .alexnet import Alex<PERSON>
# from .cpm import CPM
# from .hourglass import HourglassNet
# from .hourglass_ae import HourglassAENet
# from .hrformer import HR<PERSON>ormer
# from .hrnet import HRNet
# from .litehrnet import LiteHR<PERSON>
# from .mobilenet_v2 import MobileNetV2
# from .mobilenet_v3 import MobileNetV3
# from .mspn import MSPN
# from .regnet import RegNet
# from .resnest import ResNeSt
# from .resnet import ResNet, ResNetV1d
# from .resnext import ResNeXt
# from .rsn import RSN
# from .scnet import SCNet
# from .seresnet import SEResNet
# from .seresnext import SEResNeXt
# from .shufflenet_v1 import ShuffleNetV1
# from .shufflenet_v2 import ShuffleNetV2
# from .tcn import TCN
# from .v2v_net import V2VNet
# from .vgg import VGG
# from .vipnas_mbv3 import ViPNAS_MobileNetV3
# from .vipnas_resnet import ViPNAS_ResNet
from .vit import ViT

# __all__ = [
#     'AlexNet', 'HourglassNet', 'HourglassAENet', 'HRNet', 'MobileNetV2',
#     'MobileNetV3', 'RegNet', 'ResNet', 'ResNetV1d', 'ResNeXt', 'SCNet',
#     'SEResNet', 'SEResNeXt', 'ShuffleNetV1', 'ShuffleNetV2', 'CPM', 'RSN',
#     'MSPN', 'ResNeSt', 'VGG', 'TCN', 'ViPNAS_ResNet', 'ViPNAS_MobileNetV3',
#     'LiteHRNet', 'V2VNet', 'HRFormer', 'ViT'
# ]
